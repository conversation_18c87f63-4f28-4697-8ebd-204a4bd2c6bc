from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import List, Optional

# 用户相关的Pydantic模型
class UserBase(BaseModel):
    username: str
    email: str

class UserCreate(UserBase):
    pass

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None

class User(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 文章相关的Pydantic模型
class PostBase(BaseModel):
    title: str
    content: Optional[str] = None

class PostCreate(PostBase):
    user_id: int

class PostUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None

class Post(PostBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    author: User
    
    class Config:
        from_attributes = True

# 用户详情（包含文章列表）
class UserDetail(User):
    posts: List[Post] = []

# API响应模型
class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None
