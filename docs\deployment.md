# 部署说明

## 本地开发环境

### 前置要求
- Docker Desktop (Windows/Mac) 或 Docker Engine (Linux)
- Docker Compose

### 启动步骤
1. 克隆项目到本地
2. 进入项目目录
3. 复制环境变量文件：`cp .env.example .env`
4. 启动服务：`docker-compose up --build`

## 生产环境部署

### 方式一：镜像部署
1. **构建镜像**
   ```bash
   # 构建前端镜像
   docker build -t myapp-frontend:latest ./frontend
   
   # 构建后端镜像
   docker build -t myapp-backend:latest ./backend
   ```

2. **推送到镜像仓库**
   ```bash
   # 登录Docker Hub
   docker login
   
   # 推送镜像
   docker push myapp-frontend:latest
   docker push myapp-backend:latest
   ```

3. **服务器部署**
   ```bash
   # 拉取镜像
   docker pull myapp-frontend:latest
   docker pull myapp-backend:latest
   
   # 运行容器
   docker-compose -f docker-compose.prod.yml up -d
   ```

### 方式二：代码部署
1. 将项目代码上传到服务器
2. 在服务器上执行：
   ```bash
   docker-compose -f docker-compose.prod.yml up --build -d
   ```

### 方式三：CI/CD自动部署
1. 配置GitHub Actions或GitLab CI
2. 代码提交后自动构建和部署
3. 实现持续集成和持续部署

## 生产环境注意事项

### 安全配置
- 修改默认密码
- 使用HTTPS
- 配置防火墙
- 定期更新镜像

### 性能优化
- 使用多阶段构建减小镜像大小
- 配置资源限制
- 使用缓存优化构建速度

### 监控和日志
- 配置日志收集
- 设置监控告警
- 定期备份数据

### 扩展性
- 使用负载均衡
- 配置多副本
- 实现水平扩展

## 常见问题

### 端口冲突
- 检查端口是否被占用
- 修改docker-compose.yml中的端口映射

### 数据库连接失败
- 检查数据库服务是否启动
- 验证连接字符串配置
- 确认网络连通性

### 镜像构建失败
- 检查Dockerfile语法
- 确认基础镜像可用
- 查看构建日志定位问题
