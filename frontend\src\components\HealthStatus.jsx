import { useState, useEffect } from 'react'
import { apiService } from '../api'

function HealthStatus() {
  const [healthData, setHealthData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    checkHealth()
  }, [])

  const checkHealth = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await apiService.healthCheck()
      setHealthData(response.data)
    } catch (err) {
      setError('无法连接到后端API服务')
      console.error('健康检查失败:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <div className="loading">正在检查系统状态...</div>
  }

  return (
    <div className="section">
      <h2>🏥 系统健康状态</h2>
      
      <button className="btn" onClick={checkHealth} style={{ marginBottom: '2rem' }}>
        🔄 刷新状态
      </button>

      {error && (
        <div className="error">
          ❌ {error}
        </div>
      )}

      {healthData && (
        <div className="grid">
          <div className="card">
            <h3>🚀 API服务</h3>
            <div className="status healthy">运行正常</div>
            <p>状态: {healthData.status}</p>
            <p>消息: {healthData.message}</p>
          </div>

          <div className="card">
            <h3>🐳 Docker容器</h3>
            <div className="status healthy">运行正常</div>
            <p>前端容器: React + Vite</p>
            <p>后端容器: Python + FastAPI</p>
            <p>数据库容器: PostgreSQL</p>
          </div>

          <div className="card">
            <h3>🌐 网络通信</h3>
            <div className="status healthy">连接正常</div>
            <p>前端 → 后端: ✅ 正常</p>
            <p>后端 → 数据库: ✅ 正常</p>
            <p>容器间网络: ✅ 正常</p>
          </div>

          <div className="card">
            <h3>📊 学习进度</h3>
            <div className="status healthy">进行中</div>
            <p>✅ Docker基础概念</p>
            <p>✅ 容器化应用</p>
            <p>✅ 多容器编排</p>
            <p>✅ 前后端分离</p>
            <p>✅ 数据库集成</p>
          </div>
        </div>
      )}

      <div style={{ marginTop: '2rem', padding: '1.5rem', background: '#f8f9fa', borderRadius: '10px' }}>
        <h3 style={{ color: '#667eea', marginBottom: '1rem' }}>🎓 Docker学习要点</h3>
        <ul style={{ lineHeight: '1.8', color: '#555' }}>
          <li><strong>容器化优势:</strong> 环境一致性、快速部署、资源隔离</li>
          <li><strong>服务编排:</strong> 通过docker-compose管理多个容器</li>
          <li><strong>网络通信:</strong> 容器间通过服务名进行通信</li>
          <li><strong>数据持久化:</strong> 使用Volume保存数据库数据</li>
          <li><strong>开发效率:</strong> 热重载、统一开发环境</li>
        </ul>
      </div>
    </div>
  )
}

export default HealthStatus
