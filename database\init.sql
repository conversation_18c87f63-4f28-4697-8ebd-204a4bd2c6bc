-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文章表
CREATE TABLE IF NOT EXISTS posts (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例数据
INSERT INTO users (username, email) VALUES 
    ('admin', '<EMAIL>'),
    ('user1', '<EMAIL>'),
    ('user2', '<EMAIL>')
ON CONFLICT (username) DO NOTHING;

INSERT INTO posts (title, content, user_id) VALUES 
    ('欢迎使用Docker学习项目', '这是一个用于学习Docker的示例项目，包含前后端分离架构。', 1),
    ('Docker容器化的优势', 'Docker可以帮助我们实现应用的快速部署和扩展。', 1),
    ('学习心得', '通过实践项目学习Docker是最有效的方式。', 2)
ON CONFLICT DO NOTHING;
