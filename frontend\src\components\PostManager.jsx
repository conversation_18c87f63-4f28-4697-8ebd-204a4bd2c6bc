import { useState, useEffect } from 'react'
import { apiService } from '../api'

function PostManager() {
  const [posts, setPosts] = useState([])
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(null)
  const [formData, setFormData] = useState({ title: '', content: '', user_id: '' })
  const [editingPost, setEditingPost] = useState(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    setError(null)
    try {
      const [postsResponse, usersResponse] = await Promise.all([
        apiService.getPosts(),
        apiService.getUsers()
      ])
      setPosts(postsResponse.data)
      setUsers(usersResponse.data)
    } catch (err) {
      setError('获取数据失败')
      console.error('获取数据失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    try {
      if (editingPost) {
        await apiService.updatePost(editingPost.id, {
          title: formData.title,
          content: formData.content
        })
        setSuccess('文章更新成功！')
        setEditingPost(null)
      } else {
        await apiService.createPost(formData)
        setSuccess('文章创建成功！')
      }
      setFormData({ title: '', content: '', user_id: '' })
      fetchData()
    } catch (err) {
      setError(editingPost ? '更新文章失败' : '创建文章失败')
      console.error('操作失败:', err)
    }
  }

  const handleEdit = (post) => {
    setEditingPost(post)
    setFormData({ 
      title: post.title, 
      content: post.content, 
      user_id: post.user_id 
    })
  }

  const handleDelete = async (postId) => {
    if (!confirm('确定要删除这篇文章吗？')) return

    setError(null)
    setSuccess(null)
    try {
      await apiService.deletePost(postId)
      setSuccess('文章删除成功！')
      fetchData()
    } catch (err) {
      setError('删除文章失败')
      console.error('删除失败:', err)
    }
  }

  const cancelEdit = () => {
    setEditingPost(null)
    setFormData({ title: '', content: '', user_id: '' })
  }

  if (loading) {
    return <div className="loading">正在加载文章数据...</div>
  }

  return (
    <div className="section">
      <h2>📝 文章管理</h2>

      {error && <div className="error">{error}</div>}
      {success && <div className="success">{success}</div>}

      <form onSubmit={handleSubmit} style={{ marginBottom: '2rem' }}>
        <h3>{editingPost ? '编辑文章' : '创建新文章'}</h3>
        
        <div className="form-group">
          <label>标题:</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            required
            placeholder="请输入文章标题"
          />
        </div>

        <div className="form-group">
          <label>内容:</label>
          <textarea
            value={formData.content}
            onChange={(e) => setFormData({ ...formData, content: e.target.value })}
            placeholder="请输入文章内容"
            rows="5"
          />
        </div>

        {!editingPost && (
          <div className="form-group">
            <label>作者:</label>
            <select
              value={formData.user_id}
              onChange={(e) => setFormData({ ...formData, user_id: e.target.value })}
              required
            >
              <option value="">请选择作者</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.username} ({user.email})
                </option>
              ))}
            </select>
          </div>
        )}

        <div>
          <button type="submit" className="btn">
            {editingPost ? '更新文章' : '创建文章'}
          </button>
          {editingPost && (
            <button type="button" className="btn btn-secondary" onClick={cancelEdit}>
              取消编辑
            </button>
          )}
        </div>
      </form>

      <h3>文章列表 ({posts.length})</h3>
      {posts.length === 0 ? (
        <p style={{ color: '#666', fontStyle: 'italic' }}>暂无文章数据</p>
      ) : (
        <div className="grid">
          {posts.map(post => (
            <div key={post.id} className="card">
              <h4>{post.title}</h4>
              <p>{post.content}</p>
              <div className="card-meta">
                <p>👤 作者: {post.author?.username} ({post.author?.email})</p>
                <p>📅 创建时间: {new Date(post.created_at).toLocaleString()}</p>
                <p>🔄 更新时间: {new Date(post.updated_at).toLocaleString()}</p>
              </div>
              <div>
                <button 
                  className="btn" 
                  onClick={() => handleEdit(post)}
                  style={{ marginRight: '0.5rem' }}
                >
                  编辑
                </button>
                <button 
                  className="btn btn-danger" 
                  onClick={() => handleDelete(post.id)}
                >
                  删除
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default PostManager
