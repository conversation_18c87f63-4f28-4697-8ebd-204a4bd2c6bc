# Docker学习项目

## 项目简介
这是一个用于学习Docker的前后端分离项目，包含：
- 前端：React + Vite
- 后端：Python + FastAPI
- 数据库：PostgreSQL
- 容器化：Docker + Docker Compose

## 项目结构
```
docker/
├── docs/                    # 文档目录
│   ├── README.md           # 项目说明
│   ├── docker-guide.md     # Docker使用指南
│   └── deployment.md       # 部署说明
├── frontend/               # 前端项目
│   ├── src/
│   ├── package.json
│   ├── Dockerfile
│   └── .dockerignore
├── backend/                # 后端项目
│   ├── app/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── .dockerignore
├── database/               # 数据库相关
│   └── init.sql           # 初始化脚本
├── docker-compose.yml      # Docker编排文件
├── .env                    # 环境变量
└── .gitignore
```

## 学习目标
1. 理解Docker容器化概念
2. 学会编写Dockerfile
3. 掌握Docker Compose多容器编排
4. 了解前后端分离架构在容器中的实现
5. 学习容器间网络通信
6. 掌握数据持久化
7. 了解生产环境部署流程

## 快速开始
```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs
```

## 访问地址
- 前端：http://localhost:8080
- 后端API：http://localhost:8000
- 后端API文档：http://localhost:8000/docs
- 数据库：localhost:5432
