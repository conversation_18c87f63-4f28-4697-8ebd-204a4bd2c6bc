import { useState, useEffect } from 'react'
import { apiService } from './api'
import UserManager from './components/UserManager'
import PostManager from './components/PostManager'
import HealthStatus from './components/HealthStatus'

function App() {
  const [activeTab, setActiveTab] = useState('health')
  const [apiStatus, setApiStatus] = useState('checking')

  useEffect(() => {
    checkApiHealth()
  }, [])

  const checkApiHealth = async () => {
    try {
      await apiService.healthCheck()
      setApiStatus('healthy')
    } catch (error) {
      console.error('API健康检查失败:', error)
      setApiStatus('error')
    }
  }

  const tabs = [
    { id: 'health', label: '系统状态', component: HealthStatus },
    { id: 'users', label: '用户管理', component: UserManager },
    { id: 'posts', label: '文章管理', component: PostManager },
  ]

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component

  return (
    <div className="container">
      <header className="header">
        <h1>🐳 Docker学习项目</h1>
        <p>前后端分离 + 数据库 + 容器化部署</p>
        <div style={{ marginTop: '1rem' }}>
          <span className={`status ${apiStatus}`}>
            API状态: {apiStatus === 'healthy' ? '正常' : apiStatus === 'error' ? '异常' : '检查中...'}
          </span>
        </div>
      </header>

      <nav style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`btn ${activeTab === tab.id ? '' : 'btn-secondary'}`}
              onClick={() => setActiveTab(tab.id)}
              style={{
                background: activeTab === tab.id 
                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                  : '#f8f9fa',
                color: activeTab === tab.id ? 'white' : '#333',
                border: activeTab === tab.id ? 'none' : '2px solid #ddd'
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </nav>

      <main>
        {ActiveComponent && <ActiveComponent />}
      </main>

      <footer style={{ 
        textAlign: 'center', 
        marginTop: '3rem', 
        padding: '2rem',
        color: '#666',
        borderTop: '1px solid #eee'
      }}>
        <p>🎯 学习目标：理解Docker容器化、前后端分离架构、数据库集成</p>
        <p style={{ marginTop: '0.5rem', fontSize: '0.9rem' }}>
          前端: React + Vite | 后端: Python + FastAPI | 数据库: PostgreSQL
        </p>
      </footer>
    </div>
  )
}

export default App
