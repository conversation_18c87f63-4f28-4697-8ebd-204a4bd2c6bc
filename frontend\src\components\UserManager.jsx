import { useState, useEffect } from 'react'
import { apiService } from '../api'

function UserManager() {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(null)
  const [formData, setFormData] = useState({ username: '', email: '' })
  const [editingUser, setEditingUser] = useState(null)

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await apiService.getUsers()
      setUsers(response.data)
    } catch (err) {
      setError('获取用户列表失败')
      console.error('获取用户失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    try {
      if (editingUser) {
        await apiService.updateUser(editingUser.id, formData)
        setSuccess('用户更新成功！')
        setEditingUser(null)
      } else {
        await apiService.createUser(formData)
        setSuccess('用户创建成功！')
      }
      setFormData({ username: '', email: '' })
      fetchUsers()
    } catch (err) {
      setError(editingUser ? '更新用户失败' : '创建用户失败')
      console.error('操作失败:', err)
    }
  }

  const handleEdit = (user) => {
    setEditingUser(user)
    setFormData({ username: user.username, email: user.email })
  }

  const handleDelete = async (userId) => {
    if (!confirm('确定要删除这个用户吗？')) return

    setError(null)
    setSuccess(null)
    try {
      await apiService.deleteUser(userId)
      setSuccess('用户删除成功！')
      fetchUsers()
    } catch (err) {
      setError('删除用户失败')
      console.error('删除失败:', err)
    }
  }

  const cancelEdit = () => {
    setEditingUser(null)
    setFormData({ username: '', email: '' })
  }

  if (loading) {
    return <div className="loading">正在加载用户数据...</div>
  }

  return (
    <div className="section">
      <h2>👥 用户管理</h2>

      {error && <div className="error">{error}</div>}
      {success && <div className="success">{success}</div>}

      <form onSubmit={handleSubmit} style={{ marginBottom: '2rem' }}>
        <h3>{editingUser ? '编辑用户' : '创建新用户'}</h3>
        
        <div className="form-group">
          <label>用户名:</label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            required
            placeholder="请输入用户名"
          />
        </div>

        <div className="form-group">
          <label>邮箱:</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            required
            placeholder="请输入邮箱地址"
          />
        </div>

        <div>
          <button type="submit" className="btn">
            {editingUser ? '更新用户' : '创建用户'}
          </button>
          {editingUser && (
            <button type="button" className="btn btn-secondary" onClick={cancelEdit}>
              取消编辑
            </button>
          )}
        </div>
      </form>

      <h3>用户列表 ({users.length})</h3>
      {users.length === 0 ? (
        <p style={{ color: '#666', fontStyle: 'italic' }}>暂无用户数据</p>
      ) : (
        <div className="grid">
          {users.map(user => (
            <div key={user.id} className="card">
              <h4>{user.username}</h4>
              <p>📧 {user.email}</p>
              <div className="card-meta">
                <p>创建时间: {new Date(user.created_at).toLocaleString()}</p>
                <p>更新时间: {new Date(user.updated_at).toLocaleString()}</p>
              </div>
              <div>
                <button 
                  className="btn" 
                  onClick={() => handleEdit(user)}
                  style={{ marginRight: '0.5rem' }}
                >
                  编辑
                </button>
                <button 
                  className="btn btn-danger" 
                  onClick={() => handleDelete(user.id)}
                >
                  删除
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default UserManager
