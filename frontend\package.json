{"name": "docker-learning-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host 0.0.0.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^4.5.0"}}