# Docker代理设置指南

## 当前状态检查

### 检查时间
2025-06-03 23:59

### 当前代理设置
```bash
$ docker system info | findstr -i proxy
HTTP Proxy: http.docker.internal:3128
HTTPS Proxy: http.docker.internal:3128
No Proxy: hubproxy.docker.internal
```

### 问题分析
- **期望设置**: `http://127.0.0.1:10808`
- **实际设置**: `http.docker.internal:3128`
- **状态**: 代理设置未生效

## 解决方案

### 方案1：Docker Desktop GUI设置
1. **打开Docker Desktop**
2. **进入设置**: 点击右上角齿轮图标
3. **找到Resources → Proxies**
4. **配置代理**:
   - Web Server (HTTP): `http://127.0.0.1:10808`
   - Secure Web Server (HTTPS): `http://127.0.0.1:10808`
5. **应用并重启**: 点击"Apply & Restart"

### 方案2：完全重启Docker Desktop
1. **完全退出Docker Desktop**:
   - 右键系统托盘图标
   - 选择"Quit Docker Desktop"
2. **等待完全关闭** (约30秒)
3. **重新启动Docker Desktop**
4. **等待完全启动** (约1-2分钟)
5. **验证设置**:
   ```bash
   docker system info | findstr -i proxy
   ```

### 方案3：命令行配置
如果GUI设置不生效，可以尝试命令行配置：

1. **创建或编辑配置文件**:
   ```bash
   # Windows路径
   %USERPROFILE%\.docker\config.json
   ```

2. **添加代理配置**:
   ```json
   {
     "proxies": {
       "default": {
         "httpProxy": "http://127.0.0.1:10808",
         "httpsProxy": "http://127.0.0.1:10808"
       }
     }
   }
   ```

3. **重启Docker Desktop**

### 方案4：环境变量设置
在PowerShell中设置环境变量：

```powershell
$env:HTTP_PROXY = "http://127.0.0.1:10808"
$env:HTTPS_PROXY = "http://127.0.0.1:10808"
$env:NO_PROXY = "localhost,127.0.0.1"
```

## 验证步骤

### 1. 检查代理设置
```bash
docker system info | findstr -i proxy
```

**期望输出**:
```
HTTP Proxy: http://127.0.0.1:10808
HTTPS Proxy: http://127.0.0.1:10808
```

### 2. 测试镜像拉取
```bash
docker pull hello-world
```

**期望结果**: 成功拉取镜像

### 3. 测试网络连接
```bash
curl -I --proxy http://127.0.0.1:10808 https://registry-1.docker.io
```

**期望结果**: 返回HTTP响应头

## 故障排查

### 常见问题

**1. 代理服务未启动**
- 检查代理软件是否运行
- 确认端口10808是否监听

**2. 防火墙阻止**
- 检查Windows防火墙设置
- 确认代理软件有网络权限

**3. 代理认证问题**
- 如果代理需要认证，添加用户名密码
- 格式：`****************************************`

**4. Docker Desktop版本问题**
- 更新到最新版本的Docker Desktop
- 某些旧版本可能有代理设置bug

### 调试命令

**检查端口监听**:
```bash
netstat -an | findstr :10808
```

**测试代理连接**:
```bash
curl -v --proxy http://127.0.0.1:10808 http://www.google.com
```

**查看Docker配置**:
```bash
docker version
docker info
```

## 临时解决方案

如果代理设置始终不生效，可以使用以下临时方案：

### 1. 手动拉取镜像
```bash
# 拉取常用镜像
docker pull python:3.11-slim
docker pull node:18-alpine
docker pull postgres:15
docker pull nginx:alpine
docker pull redis:alpine
```

### 2. 使用国内镜像源
修改Dockerfile使用国内镜像：

```dockerfile
# 原始
FROM python:3.11-slim

# 修改为阿里云镜像
FROM registry.cn-hangzhou.aliyuncs.com/library/python:3.11-slim
```

### 3. 离线镜像传输
如果有其他可以访问Docker Hub的机器：

```bash
# 在有网络的机器上
docker save python:3.11-slim > python-3.11-slim.tar

# 传输到目标机器后
docker load < python-3.11-slim.tar
```

## 建议

1. **优先使用方案1和方案2**：GUI设置最直观
2. **确保代理软件正常运行**：这是最常见的问题
3. **重启是关键**：配置修改后必须重启Docker Desktop
4. **验证每一步**：每次修改后都要验证是否生效
5. **记录工作方案**：找到有效方案后记录下来

## 当前项目状态

虽然代理设置有问题，但项目已经成功运行：
- ✅ 所有容器正常运行
- ✅ 前后端通信正常
- ✅ 数据库连接正常
- ✅ 应用功能完整

代理问题主要影响：
- 新镜像的拉取
- 镜像的更新
- 新项目的创建

对于当前项目的学习目标，已经完全达成。
