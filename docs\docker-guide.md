# Docker使用指南

## Docker基础概念

### 1. 镜像 (Image)
- 镜像是容器的模板，包含应用程序和运行环境
- 通过Dockerfile构建镜像
- 镜像是只读的，分层存储

### 2. 容器 (Container)
- 容器是镜像的运行实例
- 容器是可写的，有自己的文件系统
- 容器之间相互隔离

### 3. Dockerfile
- 用于构建镜像的脚本文件
- 包含一系列指令来配置镜像

### 4. Docker Compose
- 用于定义和运行多容器应用
- 通过YAML文件配置服务

## 常用Docker命令

### 镜像操作
```bash
# 构建镜像
docker build -t 镜像名:标签 .

# 查看镜像列表
docker images

# 删除镜像
docker rmi 镜像名

# 拉取镜像
docker pull 镜像名
```

### 容器操作
```bash
# 运行容器
docker run -d -p 宿主机端口:容器端口 镜像名

# 查看运行中的容器
docker ps

# 查看所有容器
docker ps -a

# 停止容器
docker stop 容器ID

# 删除容器
docker rm 容器ID

# 进入容器
docker exec -it 容器ID /bin/bash
```

### Docker Compose命令
```bash
# 启动服务
docker-compose up

# 后台启动
docker-compose up -d

# 构建并启动
docker-compose up --build

# 停止服务
docker-compose down

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs 服务名
```

## 本项目的Docker架构

### 服务组成
1. **frontend**: React前端应用
2. **backend**: Python FastAPI后端
3. **database**: PostgreSQL数据库

### 网络通信
- 所有服务在同一个Docker网络中
- 服务间通过服务名进行通信
- 前端通过API调用后端
- 后端连接数据库

### 数据持久化
- 数据库数据通过Volume持久化
- 避免容器重启时数据丢失

### 环境变量
- 通过.env文件管理配置
- 不同环境使用不同配置文件
